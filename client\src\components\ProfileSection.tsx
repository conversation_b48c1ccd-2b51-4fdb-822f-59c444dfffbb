import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Edit, DollarSign, Clock } from 'lucide-react';
import { User } from '@shared/schema';
import { Link } from 'wouter';

interface ProfileSectionProps {
  user: User;
}

export function ProfileSection({ user }: ProfileSectionProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-foreground">Your Profile</h2>
          <Button variant="ghost" size="sm" asChild data-testid="button-edit-profile">
            <Link href="/profile/edit">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
        
        <div className="flex items-start space-x-4">
          <Avatar className="w-16 h-16">
            <AvatarImage src={user.avatar || undefined} />
            <AvatarFallback className="text-lg">{user.fullName?.charAt(0)}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-foreground" data-testid="text-user-name">
              {user.fullName}
            </h3>
            <p className="text-muted-foreground" data-testid="text-user-role">
              {user.role === 'developer' ? 'Full Stack Developer' : user.role}
            </p>
            {user.location && (
              <p className="text-sm text-muted-foreground mt-1" data-testid="text-user-location">
                {user.location}
              </p>
            )}
            
            <div className="flex items-center mt-2 space-x-4">
              {user.hourlyRate && (
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1" />
                  <span className="text-sm bg-accent text-accent-foreground px-2 py-1 rounded-full" data-testid="text-hourly-rate">
                    ${user.hourlyRate}/hr
                  </span>
                </div>
              )}
              {user.availability && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-sm text-muted-foreground" data-testid="text-availability">
                    {user.availability}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {user.bio && (
          <div className="mt-4">
            <p className="text-sm text-muted-foreground" data-testid="text-user-bio">
              {user.bio}
            </p>
          </div>
        )}
        
        {user.skills && user.skills.length > 0 && (
          <div className="mt-4">
            <p className="text-sm text-muted-foreground mb-3">Skills</p>
            <div className="flex flex-wrap gap-2">
              {user.skills.map((skill, index) => (
                <Badge 
                  key={index}
                  className="skill-tag bg-gradient-to-r from-primary to-accent text-white"
                  data-testid={`badge-skill-${index}`}
                >
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
