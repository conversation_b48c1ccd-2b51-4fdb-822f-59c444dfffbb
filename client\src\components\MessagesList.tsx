import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Message, User } from '@shared/schema';
import { formatDistanceToNow } from 'date-fns';

interface MessagesListProps {
  messages: (Message & {
    sender?: User;
    receiver?: User;
  })[];
  currentUserId: string;
}

export function MessagesList({ messages, currentUserId }: MessagesListProps) {
  if (messages.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No messages yet</p>
      </div>
    );
  }

  // Group messages by conversation partner
  const conversations = messages.reduce((acc, message) => {
    const partnerId = message.senderId === currentUserId ? message.receiverId : message.senderId;
    if (!acc[partnerId]) {
      acc[partnerId] = [];
    }
    acc[partnerId].push(message);
    return acc;
  }, {} as Record<string, typeof messages>);

  return (
    <div className="space-y-3">
      {Object.entries(conversations).map(([partnerId, partnerMessages]) => {
        const latestMessage = partnerMessages[0];
        const partner = latestMessage.senderId === currentUserId 
          ? latestMessage.receiver 
          : latestMessage.sender;
        
        return (
          <div 
            key={partnerId}
            className="flex items-start space-x-3 p-3 hover:bg-muted rounded-lg cursor-pointer transition-colors"
            data-testid={`conversation-${partnerId}`}
          >
            <Avatar className="w-10 h-10">
              <AvatarImage src={partner?.avatar || undefined} />
              <AvatarFallback>{partner?.fullName?.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-foreground" data-testid={`text-partner-name-${partnerId}`}>
                  {partner?.fullName}
                </p>
                <span className="text-xs text-muted-foreground" data-testid={`text-message-time-${partnerId}`}>
                  {formatDistanceToNow(new Date(latestMessage.createdAt || ''), { addSuffix: true })}
                </span>
              </div>
              <p className="text-xs text-muted-foreground truncate" data-testid={`text-message-content-${partnerId}`}>
                {latestMessage.content}
              </p>
              {!latestMessage.isRead && latestMessage.senderId !== currentUserId && (
                <div className="w-2 h-2 bg-primary rounded-full mt-1" data-testid={`indicator-unread-${partnerId}`} />
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
