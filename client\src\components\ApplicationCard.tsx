import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, DollarSign } from 'lucide-react';
import { Application } from '@shared/schema';
import { formatDistanceToNow } from 'date-fns';

interface ApplicationCardProps {
  application: Application & {
    project?: { title: string };
  };
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  under_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  interview: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  accepted: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
};

export function ApplicationCard({ application }: ApplicationCardProps) {
  return (
    <Card className="hover:shadow-md transition-all" data-testid={`card-application-${application.id}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium text-foreground" data-testid={`text-project-title-${application.id}`}>
            {application.project?.title || 'Unknown Project'}
          </h3>
          <Badge 
            className={statusColors[application.status]}
            data-testid={`badge-status-${application.id}`}
          >
            {application.status.replace('_', ' ')}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            <span data-testid={`text-applied-date-${application.id}`}>
              Applied {formatDistanceToNow(new Date(application.createdAt || ''), { addSuffix: true })}
            </span>
          </div>
          <div className="flex items-center font-medium">
            <DollarSign className="h-3 w-3 mr-1" />
            <span data-testid={`text-quoted-price-${application.id}`}>
              {application.quotedPrice}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
