import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { 
  insertUserSchema, insertProjectSchema, insertApplicationSchema, 
  insertReviewSchema, insertMessageSchema 
} from "@shared/schema";
import admin from "firebase-admin";

interface AuthenticatedRequest extends Request {
  user?: admin.auth.DecodedIdToken;
}

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
  });
}

// Middleware to verify Firebase token
async function verifyFirebaseToken(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  const token = req.headers.authorization?.split('Bearer ')[1];
  
  if (!token) {
    return res.status(401).json({ message: "No token provided" });
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    return res.status(401).json({ message: "Invalid token" });
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth routes
  app.post("/api/auth/register", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userData = insertUserSchema.parse({
        ...req.body,
        firebaseUid: req.user!.uid,
        email: req.user!.email,
      });
      
      const user = await storage.createUser(userData);
      res.json(user);
    } catch (error: any) {
      res.status(400).json({ message: "Registration failed", error: error.message });
    }
  });

  app.get("/api/auth/me", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const user = await storage.getUserByFirebaseUid(req.user!.uid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get user", error: error.message });
    }
  });

  // User routes
  app.get("/api/users", async (req: Request, res: Response) => {
    try {
      const users = await storage.getDevelopers();
      res.json(users);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get users", error: error.message });
    }
  });

  app.get("/api/users/:id", async (req: Request, res: Response) => {
    try {
      const user = await storage.getUser(req.params.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get user", error: error.message });
    }
  });

  app.put("/api/users/:id", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const updates = insertUserSchema.partial().parse(req.body);
      const user = await storage.updateUser(req.params.id, updates);
      res.json(user);
    } catch (error: any) {
      res.status(400).json({ message: "Failed to update user", error: error.message });
    }
  });

  // Project routes
  app.get("/api/projects", async (req: Request, res: Response) => {
    try {
      const projects = await storage.getProjects();
      res.json(projects);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get projects", error: error.message });
    }
  });

  app.get("/api/projects/:id", async (req: Request, res: Response) => {
    try {
      const project = await storage.getProject(req.params.id);
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }
      res.json(project);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get project", error: error.message });
    }
  });

  app.post("/api/projects", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const user = await storage.getUserByFirebaseUid(req.user!.uid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const projectData = insertProjectSchema.parse({
        ...req.body,
        clientId: user.id,
      });
      
      const project = await storage.createProject(projectData);
      res.json(project);
    } catch (error: any) {
      res.status(400).json({ message: "Failed to create project", error: error.message });
    }
  });

  app.put("/api/projects/:id", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const updates = insertProjectSchema.partial().parse(req.body);
      const project = await storage.updateProject(req.params.id, updates);
      res.json(project);
    } catch (error: any) {
      res.status(400).json({ message: "Failed to update project", error: error.message });
    }
  });

  // Application routes
  app.get("/api/applications/project/:projectId", async (req: Request, res: Response) => {
    try {
      const applications = await storage.getApplicationsByProject(req.params.projectId);
      res.json(applications);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get applications", error: error.message });
    }
  });

  app.get("/api/applications/developer/:developerId", async (req: Request, res: Response) => {
    try {
      const applications = await storage.getApplicationsByDeveloper(req.params.developerId);
      res.json(applications);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get applications", error: error.message });
    }
  });

  app.post("/api/applications", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const user = await storage.getUserByFirebaseUid(req.user!.uid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const applicationData = insertApplicationSchema.parse({
        ...req.body,
        developerId: user.id,
      });
      
      const application = await storage.createApplication(applicationData);
      res.json(application);
    } catch (error: any) {
      res.status(400).json({ message: "Failed to create application", error: error.message });
    }
  });

  app.put("/api/applications/:id", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const updates = insertApplicationSchema.partial().parse(req.body);
      const application = await storage.updateApplication(req.params.id, updates);
      res.json(application);
    } catch (error: any) {
      res.status(400).json({ message: "Failed to update application", error: error.message });
    }
  });

  // Review routes
  app.get("/api/reviews/project/:projectId", async (req: Request, res: Response) => {
    try {
      const reviews = await storage.getReviewsByProject(req.params.projectId);
      res.json(reviews);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get reviews", error: error.message });
    }
  });

  app.get("/api/reviews/user/:userId", async (req: Request, res: Response) => {
    try {
      const reviews = await storage.getReviewsByUser(req.params.userId);
      res.json(reviews);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get reviews", error: error.message });
    }
  });

  app.post("/api/reviews", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const user = await storage.getUserByFirebaseUid(req.user!.uid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const reviewData = insertReviewSchema.parse({
        ...req.body,
        reviewerId: user.id,
      });
      
      const review = await storage.createReview(reviewData);
      res.json(review);
    } catch (error: any) {
      res.status(400).json({ message: "Failed to create review", error: error.message });
    }
  });

  // Message routes
  app.get("/api/messages/:userId", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const user = await storage.getUserByFirebaseUid(req.user!.uid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const messages = await storage.getMessagesBetweenUsers(user.id, req.params.userId);
      res.json(messages);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get messages", error: error.message });
    }
  });

  app.get("/api/conversations", verifyFirebaseToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const user = await storage.getUserByFirebaseUid(req.user!.uid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const conversations = await storage.getRecentConversations(user.id);
      res.json(conversations);
    } catch (error: any) {
      res.status(500).json({ message: "Failed to get conversations", error: error.message });
    }
  });

  const httpServer = createServer(app);

  // WebSocket server for real-time messaging
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  wss.on('connection', (ws: WebSocket) => {
    ws.on('message', async (data: string) => {
      try {
        const message = JSON.parse(data);
        
        if (message.type === 'send_message') {
          const messageData = insertMessageSchema.parse({
            senderId: message.senderId,
            receiverId: message.receiverId,
            projectId: message.projectId,
            content: message.content,
          });
          
          const savedMessage = await storage.createMessage(messageData);
          
          // Broadcast to all connected clients
          wss.clients.forEach((client) => {
            if (client.readyState === WebSocket.OPEN) {
              client.send(JSON.stringify({
                type: 'new_message',
                data: savedMessage,
              }));
            }
          });
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });
  });

  return httpServer;
}
