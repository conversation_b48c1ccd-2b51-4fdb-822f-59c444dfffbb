import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import NotFound from "@/pages/not-found";
import Login from "@/pages/auth/Login";
import DeveloperDashboard from "@/pages/dashboard/DeveloperDashboard";
import ClientDashboard from "@/pages/dashboard/ClientDashboard";
import AdminDashboard from "@/pages/dashboard/AdminDashboard";
import ProjectDetails from "@/pages/projects/ProjectDetails";
import CreateProject from "@/pages/projects/CreateProject";
import EditProfile from "@/pages/profile/EditProfile";
import ChatInterface from "@/pages/chat/ChatInterface";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <LoadingSpinner />
      </div>
    );
  }

  if (!user) {
    return <Login />;
  }

  return <>{children}</>;
}

function DashboardRouter() {
  const { user } = useAuth();

  if (!user) return <Login />;

  switch (user.role) {
    case 'developer':
      return <DeveloperDashboard />;
    case 'client':
      return <ClientDashboard />;
    case 'admin':
      return <AdminDashboard />;
    default:
      return <DeveloperDashboard />;
  }
}

function Router() {
  return (
    <Switch>
      <Route path="/login" component={Login} />
      <Route path="/">
        <ProtectedRoute>
          <DashboardRouter />
        </ProtectedRoute>
      </Route>
      <Route path="/dashboard">
        <ProtectedRoute>
          <DashboardRouter />
        </ProtectedRoute>
      </Route>
      <Route path="/projects/:id">
        <ProtectedRoute>
          <ProjectDetails />
        </ProtectedRoute>
      </Route>
      <Route path="/projects/create">
        <ProtectedRoute>
          <CreateProject />
        </ProtectedRoute>
      </Route>
      <Route path="/profile/edit">
        <ProtectedRoute>
          <EditProfile />
        </ProtectedRoute>
      </Route>
      <Route path="/chat">
        <ProtectedRoute>
          <ChatInterface />
        </ProtectedRoute>
      </Route>
      <Route path="/chat/:userId">
        <ProtectedRoute>
          <ChatInterface />
        </ProtectedRoute>
      </Route>
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <TooltipProvider>
            <Toaster />
            <Router />
          </TooltipProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
