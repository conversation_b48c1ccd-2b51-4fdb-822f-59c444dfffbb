<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevLinkr - Connect Developers & Clients</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <script>
    tailwind.config = {
        theme: {
            extend: {
                borderRadius: {
                    lg: "var(--radius)",
                    md: "calc(var(--radius) - 2px)",
                    sm: "calc(var(--radius) - 4px)",
                },
                colors: {
                    background: "var(--background)",
                    foreground: "var(--foreground)",
                    card: {
                        DEFAULT: "var(--card)",
                        foreground: "var(--card-foreground)",
                    },
                    popover: {
                        DEFAULT: "var(--popover)",
                        foreground: "var(--popover-foreground)",
                    },
                    primary: {
                        DEFAULT: "var(--primary)",
                        foreground: "var(--primary-foreground)",
                    },
                    secondary: {
                        DEFAULT: "var(--secondary)",
                        foreground: "var(--secondary-foreground)",
                    },
                    muted: {
                        DEFAULT: "var(--muted)",
                        foreground: "var(--muted-foreground)",
                    },
                    accent: {
                        DEFAULT: "var(--accent)",
                        foreground: "var(--accent-foreground)",
                    },
                    destructive: {
                        DEFAULT: "var(--destructive)",
                        foreground: "var(--destructive-foreground)",
                    },
                    border: "var(--border)",
                    input: "var(--input)",
                    ring: "var(--ring)",
                },
                fontFamily: {
                    sans: ["var(--font-sans)"],
                    mono: ["var(--font-mono)"],
                }
            },
        }
    };
    </script>
    <style>
        :root {
            --background: hsl(0 0% 100%);
            --foreground: hsl(222 16% 13%);
            --card: hsl(0 0% 100%);
            --card-foreground: hsl(222 16% 13%);
            --popover: hsl(0 0% 100%);
            --popover-foreground: hsl(222 16% 13%);
            --primary: hsl(217 91% 60%);
            --primary-foreground: hsl(0 0% 98%);
            --secondary: hsl(210 40% 96%);
            --secondary-foreground: hsl(215 25% 27%);
            --muted: hsl(210 40% 96%);
            --muted-foreground: hsl(215 16% 47%);
            --accent: hsl(142 76% 36%);
            --accent-foreground: hsl(0 0% 98%);
            --destructive: hsl(0 84% 60%);
            --destructive-foreground: hsl(0 0% 98%);
            --border: hsl(214 32% 91%);
            --input: hsl(214 32% 91%);
            --ring: hsl(217 91% 60%);
            --radius: 0.5rem;
            --font-sans: "Inter", system-ui, sans-serif;
            --font-mono: "JetBrains Mono", monospace;
        }
        
        * {
            font-family: var(--font-sans);
        }
        
        .mono {
            font-family: var(--font-mono);
        }

        .active-nav {
            background-color: var(--primary);
            color: var(--primary-foreground);
        }

        .notification-dot {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .project-card:hover {
            transform: translateY(-2px);
            transition: transform 0.2s ease-in-out;
        }

        .skill-tag {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            color: white;
        }
    </style>
</head>
<body class="bg-background text-foreground">
    <!-- Navigation -->
    <nav class="bg-card border-b border-border sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-code text-primary-foreground"></i>
                        </div>
                        <span class="text-xl font-bold text-foreground">DevLinkr</span>
                    </div>
                    <div class="hidden md:flex space-x-6">
                        <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">Find Developers</a>
                        <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">Browse Projects</a>
                        <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">How it Works</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="relative p-2 text-muted-foreground hover:text-foreground">
                        <i class="fas fa-bell"></i>
                        <span class="absolute top-0 right-0 w-2 h-2 bg-destructive rounded-full notification-dot"></span>
                    </button>
                    <div class="flex items-center space-x-2">
                        <!-- Profile photo showing developer role -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" alt="Profile" class="w-8 h-8 rounded-full" data-mock="true">
                        <span class="text-sm text-muted-foreground hidden sm:block" data-mock="true">Alex Chen</span>
                        <i class="fas fa-chevron-down text-muted-foreground"></i>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Dashboard Container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Dashboard Header -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-foreground">Developer Dashboard</h1>
                <p class="text-muted-foreground mt-1">Manage your profile, applications, and projects</p>
            </div>
            <div class="flex items-center space-x-3 mt-4 lg:mt-0">
                <div class="flex items-center space-x-2 bg-accent text-accent-foreground px-3 py-1 rounded-full text-sm">
                    <div class="w-2 h-2 bg-accent-foreground rounded-full"></div>
                    <span>Available</span>
                </div>
                <button class="bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:opacity-90 transition-opacity">
                    <i class="fas fa-plus mr-2"></i>Update Profile
                </button>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-card border border-border rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-muted-foreground text-sm">Active Applications</p>
                        <p class="text-2xl font-bold text-foreground" data-mock="true">12</p>
                    </div>
                    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-paper-plane text-primary"></i>
                    </div>
                </div>
                <div class="flex items-center mt-3 text-sm">
                    <span class="text-accent">+3 this week</span>
                </div>
            </div>
            <div class="bg-card border border-border rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-muted-foreground text-sm">Projects Completed</p>
                        <p class="text-2xl font-bold text-foreground" data-mock="true">24</p>
                    </div>
                    <div class="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-accent"></i>
                    </div>
                </div>
                <div class="flex items-center mt-3 text-sm">
                    <span class="text-accent">98% success rate</span>
                </div>
            </div>
            <div class="bg-card border border-border rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-muted-foreground text-sm">Total Earnings</p>
                        <p class="text-2xl font-bold text-foreground" data-mock="true">$42,350</p>
                    </div>
                    <div class="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-accent"></i>
                    </div>
                </div>
                <div class="flex items-center mt-3 text-sm">
                    <span class="text-accent">+$3,200 this month</span>
                </div>
            </div>
            <div class="bg-card border border-border rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-muted-foreground text-sm">Average Rating</p>
                        <p class="text-2xl font-bold text-foreground" data-mock="true">4.9</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-yellow-500"></i>
                    </div>
                </div>
                <div class="flex items-center mt-3 text-sm">
                    <div class="flex text-yellow-500">
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column - Profile & Recent Activity -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Developer Profile Card -->
                <div class="bg-card border border-border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-foreground">Your Profile</h2>
                        <button class="text-primary hover:text-primary/80 text-sm">
                            <i class="fas fa-edit mr-1"></i>Edit
                        </button>
                    </div>
                    <div class="flex items-start space-x-4">
                        <!-- Professional headshot of a software developer -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Alex Chen - Full Stack Developer" class="w-16 h-16 rounded-full object-cover" data-mock="true">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-foreground" data-mock="true">Alex Chen</h3>
                            <p class="text-muted-foreground" data-mock="true">Full Stack Developer</p>
                            <p class="text-sm text-muted-foreground mt-1" data-mock="true">San Francisco, CA</p>
                            <div class="flex items-center mt-2">
                                <span class="text-sm bg-accent text-accent-foreground px-2 py-1 rounded-full" data-mock="true">$85/hr</span>
                                <span class="ml-3 text-sm text-muted-foreground" data-mock="true">Available 20 hrs/week</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p class="text-sm text-muted-foreground mb-3">Skills</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="skill-tag px-3 py-1 rounded-full text-xs font-medium" data-mock="true">React</span>
                            <span class="skill-tag px-3 py-1 rounded-full text-xs font-medium" data-mock="true">Node.js</span>
                            <span class="skill-tag px-3 py-1 rounded-full text-xs font-medium" data-mock="true">TypeScript</span>
                            <span class="skill-tag px-3 py-1 rounded-full text-xs font-medium" data-mock="true">PostgreSQL</span>
                            <span class="skill-tag px-3 py-1 rounded-full text-xs font-medium" data-mock="true">AWS</span>
                        </div>
                    </div>
                </div>

                <!-- Available Projects -->
                <div class="bg-card border border-border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-foreground">Recommended Projects</h2>
                        <a href="#" class="text-primary hover:text-primary/80 text-sm">View All</a>
                    </div>
                    <div class="space-y-4">
                        <!-- @MAP: projects.map(project => ( -->
                        <div class="project-card bg-background border border-border rounded-lg p-4 hover:shadow-md transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <h3 class="font-semibold text-foreground" data-mock="true">E-commerce Mobile App Development</h3>
                                <span class="text-accent font-semibold" data-mock="true">$5,000 - $8,000</span>
                            </div>
                            <p class="text-muted-foreground text-sm mb-3" data-mock="true">
                                Looking for an experienced React Native developer to build a modern e-commerce mobile app with payment integration, user authentication, and admin dashboard.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-muted text-muted-foreground px-2 py-1 rounded text-xs" data-mock="true">React Native</span>
                                    <span class="bg-muted text-muted-foreground px-2 py-1 rounded text-xs" data-mock="true">Redux</span>
                                    <span class="bg-muted text-muted-foreground px-2 py-1 rounded text-xs" data-mock="true">Firebase</span>
                                </div>
                                <button class="bg-primary text-primary-foreground px-4 py-2 rounded-lg text-sm hover:opacity-90">
                                    Apply Now
                                </button>
                            </div>
                            <div class="flex items-center mt-3 text-xs text-muted-foreground">
                                <i class="fas fa-clock mr-1"></i>
                                <span data-mock="true">Posted 2 hours ago</span>
                                <span class="mx-2">•</span>
                                <span data-mock="true">3 proposals</span>
                            </div>
                        </div>

                        <div class="project-card bg-background border border-border rounded-lg p-4 hover:shadow-md transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <h3 class="font-semibold text-foreground" data-mock="true">SaaS Dashboard Redesign</h3>
                                <span class="text-accent font-semibold" data-mock="true">$3,500 - $5,000</span>
                            </div>
                            <p class="text-muted-foreground text-sm mb-3" data-mock="true">
                                We need a frontend developer to redesign our existing SaaS dashboard using modern React components and improve the user experience.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-muted text-muted-foreground px-2 py-1 rounded text-xs" data-mock="true">React</span>
                                    <span class="bg-muted text-muted-foreground px-2 py-1 rounded text-xs" data-mock="true">TypeScript</span>
                                    <span class="bg-muted text-muted-foreground px-2 py-1 rounded text-xs" data-mock="true">Tailwind</span>
                                </div>
                                <button class="bg-primary text-primary-foreground px-4 py-2 rounded-lg text-sm hover:opacity-90">
                                    Apply Now
                                </button>
                            </div>
                            <div class="flex items-center mt-3 text-xs text-muted-foreground">
                                <i class="fas fa-clock mr-1"></i>
                                <span data-mock="true">Posted 5 hours ago</span>
                                <span class="mx-2">•</span>
                                <span data-mock="true">7 proposals</span>
                            </div>
                        </div>
                        <!-- @END_MAP )) -->
                    </div>
                </div>
            </div>

            <!-- Right Column - Sidebar -->
            <div class="space-y-6">
                <!-- Active Applications -->
                <div class="bg-card border border-border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-foreground">Active Applications</h3>
                        <span class="text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full" data-mock="true">12</span>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-background rounded-lg border border-border">
                            <div>
                                <p class="text-sm font-medium text-foreground" data-mock="true">Fintech API Integration</p>
                                <p class="text-xs text-muted-foreground" data-mock="true">Applied 2 days ago</p>
                            </div>
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full" data-mock="true">Under Review</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-background rounded-lg border border-border">
                            <div>
                                <p class="text-sm font-medium text-foreground" data-mock="true">React Component Library</p>
                                <p class="text-xs text-muted-foreground" data-mock="true">Applied 4 days ago</p>
                            </div>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full" data-mock="true">Interview</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-background rounded-lg border border-border">
                            <div>
                                <p class="text-sm font-medium text-foreground" data-mock="true">GraphQL Migration</p>
                                <p class="text-xs text-muted-foreground" data-mock="true">Applied 1 week ago</p>
                            </div>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full" data-mock="true">Proposal Sent</span>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <div class="bg-card border border-border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-foreground">Recent Messages</h3>
                        <button class="text-primary hover:text-primary/80 text-sm">
                            <i class="fas fa-comment-dots mr-1"></i>Open Chat
                        </button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3 p-2 hover:bg-muted rounded-lg cursor-pointer">
                            <!-- Client profile photo -->
                            <img src="https://pixabay.com/get/g011762997d756b0c9b60c833cccdc2a208a4d8406d3bdb99083e6c62c453d43e47eb02c2cdfc4995d3ac66bc4e492f1873315191b18abd43cb2cf82a21401691_1280.jpg" alt="Sarah Johnson - Project Manager" class="w-8 h-8 rounded-full object-cover" data-mock="true">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-foreground" data-mock="true">Sarah Johnson</p>
                                    <span class="text-xs text-muted-foreground" data-mock="true">2m</span>
                                </div>
                                <p class="text-xs text-muted-foreground truncate" data-mock="true">Thanks for the proposal! When can we schedule a call?</p>
                                <div class="w-2 h-2 bg-primary rounded-full ml-auto mt-1"></div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3 p-2 hover:bg-muted rounded-lg cursor-pointer">
                            <!-- Client profile photo -->
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=50&h=50" alt="Mike Rodriguez - Startup Founder" class="w-8 h-8 rounded-full object-cover" data-mock="true">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-foreground" data-mock="true">Mike Rodriguez</p>
                                    <span class="text-xs text-muted-foreground" data-mock="true">1h</span>
                                </div>
                                <p class="text-xs text-muted-foreground truncate" data-mock="true">Great work on the MVP! Ready for the next phase?</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Earnings Summary -->
                <div class="bg-card border border-border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-foreground">This Month</h3>
                        <button class="text-primary hover:text-primary/80 text-sm">
                            View Details
                        </button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-muted-foreground">Total Earned</span>
                            <span class="font-semibold text-foreground" data-mock="true">$3,200</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-muted-foreground">Hours Worked</span>
                            <span class="font-semibold text-foreground" data-mock="true">64h</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-muted-foreground">Avg. Rate</span>
                            <span class="font-semibold text-foreground" data-mock="true">$85/hr</span>
                        </div>
                        <div class="pt-3 border-t border-border">
                            <div class="w-full bg-muted rounded-full h-2">
                                <div class="bg-accent h-2 rounded-full" style="width: 75%" data-mock="true"></div>
                            </div>
                            <p class="text-xs text-muted-foreground mt-2" data-mock="true">75% of monthly goal ($4,000)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Bar -->
        <div class="mt-8 bg-muted rounded-lg p-4">
            <div class="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-lightbulb text-primary-foreground"></i>
                    </div>
                    <div>
                        <p class="font-medium text-foreground">Complete your profile to get 3x more project invitations</p>
                        <p class="text-sm text-muted-foreground">Add portfolio items and detailed skills</p>
                    </div>
                </div>
                <button class="bg-accent text-accent-foreground px-6 py-2 rounded-lg hover:opacity-90 transition-opacity">
                    Complete Profile
                </button>
            </div>
        </div>
    </div>

    <!-- Role Switch Demo Modal (Hidden by default) -->
    <div id="role-demo" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-card rounded-lg p-6 max-w-md w-full">
                <h3 class="text-lg font-semibold mb-4">Switch Role Demo</h3>
                <div class="space-y-3">
                    <button class="w-full text-left p-3 border border-border rounded-lg hover:bg-muted">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-code text-primary"></i>
                            <div>
                                <p class="font-medium">Developer Dashboard</p>
                                <p class="text-sm text-muted-foreground">Current view</p>
                            </div>
                        </div>
                    </button>
                    <button onclick="showClientView()" class="w-full text-left p-3 border border-border rounded-lg hover:bg-muted">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-briefcase text-accent"></i>
                            <div>
                                <p class="font-medium">Client Dashboard</p>
                                <p class="text-sm text-muted-foreground">Switch to client view</p>
                            </div>
                        </div>
                    </button>
                    <button onclick="showAdminView()" class="w-full text-left p-3 border border-border rounded-lg hover:bg-muted">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-shield-alt text-destructive"></i>
                            <div>
                                <p class="font-medium">Admin Dashboard</p>
                                <p class="text-sm text-muted-foreground">Platform management</p>
                            </div>
                        </div>
                    </button>
                </div>
                <button onclick="closeModal()" class="mt-4 w-full bg-muted text-muted-foreground py-2 rounded-lg">
                    Close
                </button>
            </div>
        </div>
    </div>

    <script>
        // TODO: Implement business logic, API calls, and state management
        
        // Simple demo functionality
        (function() {
            // Show role switching modal
            function showRoleDemo() {
                document.getElementById('role-demo').classList.remove('hidden');
            }

            function closeModal() {
                document.getElementById('role-demo').classList.add('hidden');
            }

            // Mock role switching functions
            function showClientView() {
                // In a real app, this would navigate to client dashboard
                alert('Client dashboard view would load here with project posting and developer browsing features');
                closeModal();
            }

            function showAdminView() {
                // In a real app, this would navigate to admin dashboard
                alert('Admin dashboard view would load here with user management and moderation tools');
                closeModal();
            }

            // Add click handlers for demo
            document.querySelector('.fas.fa-chevron-down').parentElement.addEventListener('click', showRoleDemo);
            
            // Make functions globally available for onclick handlers
            window.showClientView = showClientView;
            window.showAdminView = showAdminView;
            window.closeModal = closeModal;

            // Mock notification interactions
            document.querySelector('.fas.fa-bell').addEventListener('click', function() {
                alert('Notifications panel would open here showing project updates, messages, and system alerts');
            });

            // Mock project application
            document.querySelectorAll('.project-card button').forEach(button => {
                button.addEventListener('click', function() {
                    alert('Application form would open here with proposal submission interface');
                });
            });

            // Mock message interactions
            document.querySelector('.fas.fa-comment-dots').parentElement.addEventListener('click', function() {
                alert('Real-time chat interface would open here using Firebase messaging');
            });
        })();
    </script>
</body>
</html>