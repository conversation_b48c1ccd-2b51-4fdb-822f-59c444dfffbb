import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { ProjectCard } from '@/components/ProjectCard';
import { ApplicationCard } from '@/components/ApplicationCard';
import { ProfileSection } from '@/components/ProfileSection';
import { MessagesList } from '@/components/MessagesList';
import { NotebookPen, CheckCircle, DollarSign, Star, TrendingUp } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Project, Application, Message } from '@shared/schema';

export default function DeveloperDashboard() {
  const { user } = useAuth();

  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ['/api/projects'],
    enabled: !!user,
  });

  const { data: applications, isLoading: applicationsLoading } = useQuery<Application[]>({
    queryKey: ['/api/applications/developer', user?.id],
    enabled: !!user?.id,
  });

  const { data: messages, isLoading: messagesLoading } = useQuery<Message[]>({
    queryKey: ['/api/conversations'],
    enabled: !!user?.id,
  });

  if (!user) return null;

  const stats = [
    {
      title: 'Active Applications',
      value: applications?.length || 0,
      change: '+3 this week',
      icon: NotebookPen,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: 'Projects Completed',
      value: '24',
      change: '98% success rate',
      icon: CheckCircle,
      color: 'text-accent',
      bgColor: 'bg-accent/10',
    },
    {
      title: 'Total Earnings',
      value: '$42,350',
      change: '+$3,200 this month',
      icon: DollarSign,
      color: 'text-accent',
      bgColor: 'bg-accent/10',
    },
    {
      title: 'Average Rating',
      value: '4.9',
      change: '★★★★★',
      icon: Star,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900',
    },
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Dashboard Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Developer Dashboard</h1>
            <p className="text-muted-foreground mt-1">Manage your profile, applications, and projects</p>
          </div>
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Badge variant="secondary" className="bg-accent text-accent-foreground" data-testid="badge-availability">
              <div className="w-2 h-2 bg-accent-foreground rounded-full mr-2" />
              Available
            </Badge>
            <Button data-testid="button-update-profile">
              <TrendingUp className="h-4 w-4 mr-2" />
              Update Profile
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">{stat.title}</p>
                    <p className="text-2xl font-bold text-foreground" data-testid={`stat-value-${index}`}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="flex items-center mt-3 text-sm">
                  <span className="text-accent" data-testid={`stat-change-${index}`}>{stat.change}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Profile Section */}
            <ProfileSection user={user} />

            {/* Available Projects */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-foreground">Recommended Projects</h2>
                  <Button variant="ghost" size="sm" data-testid="link-view-all-projects">
                    View All
                  </Button>
                </div>
                {projectsLoading ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner />
                  </div>
                ) : projects && projects.length > 0 ? (
                  <div className="space-y-4">
                    {projects.slice(0, 2).map((project) => (
                      <ProjectCard 
                        key={project.id} 
                        project={project}
                        onApply={(projectId) => console.log('Apply to project:', projectId)}
                      />
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title="No Projects Available"
                    description="Check back later for new project opportunities"
                    icon={<NotebookPen className="h-6 w-6" />}
                  />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Active Applications */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-foreground">Active Applications</h3>
                  <Badge variant="outline" data-testid="badge-applications-count">
                    {applications?.length || 0}
                  </Badge>
                </div>
                {applicationsLoading ? (
                  <div className="flex justify-center py-4">
                    <LoadingSpinner className="h-6 w-6" />
                  </div>
                ) : applications && applications.length > 0 ? (
                  <div className="space-y-3">
                    {applications.slice(0, 3).map((application) => (
                      <ApplicationCard key={application.id} application={application} />
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title="No Applications"
                    description="Apply to projects to see them here"
                    icon={<NotebookPen className="h-4 w-4" />}
                  />
                )}
              </CardContent>
            </Card>

            {/* Messages */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-foreground">Recent Messages</h3>
                  <Button variant="ghost" size="sm" data-testid="button-open-chat">
                    Open Chat
                  </Button>
                </div>
                {messagesLoading ? (
                  <div className="flex justify-center py-4">
                    <LoadingSpinner className="h-6 w-6" />
                  </div>
                ) : messages && messages.length > 0 ? (
                  <MessagesList messages={messages.slice(0, 3)} currentUserId={user.id} />
                ) : (
                  <EmptyState
                    title="No Messages"
                    description="Your conversations will appear here"
                    icon={<NotebookPen className="h-4 w-4" />}
                  />
                )}
              </CardContent>
            </Card>

            {/* Earnings Summary */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-foreground">This Month</h3>
                  <Button variant="ghost" size="sm" data-testid="button-view-earnings">
                    View Details
                  </Button>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Total Earned</span>
                    <span className="font-semibold text-foreground" data-testid="text-total-earned">$3,200</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Hours Worked</span>
                    <span className="font-semibold text-foreground" data-testid="text-hours-worked">64h</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Avg. Rate</span>
                    <span className="font-semibold text-foreground" data-testid="text-avg-rate">$85/hr</span>
                  </div>
                  <div className="pt-3 border-t border-border">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-accent h-2 rounded-full" style={{ width: '75%' }} data-testid="progress-monthly-goal" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2" data-testid="text-goal-progress">
                      75% of monthly goal ($4,000)
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions Bar */}
        <Card className="mt-8 bg-muted">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-primary-foreground" />
                </div>
                <div>
                  <p className="font-medium text-foreground">Complete your profile to get 3x more project invitations</p>
                  <p className="text-sm text-muted-foreground">Add portfolio items and detailed skills</p>
                </div>
              </div>
              <Button className="bg-accent text-accent-foreground hover:bg-accent/90" data-testid="button-complete-profile">
                Complete Profile
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
