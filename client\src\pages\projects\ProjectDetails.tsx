import React from 'react';
import { useParams } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { ApplicationCard } from '@/components/ApplicationCard';
import { Calendar, DollarSign, MapPin, User, Send } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Project, Application } from '@shared/schema';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';

export default function ProjectDetails() {
  const { id } = useParams();
  const { user } = useAuth();
  const { toast } = useToast();
  const [proposal, setProposal] = React.useState('');
  const [quotedPrice, setQuotedPrice] = React.useState('');

  const { data: project, isLoading: projectLoading } = useQuery<Project>({
    queryKey: ['/api/projects', id],
    enabled: !!id,
  });

  const { data: applications, isLoading: applicationsLoading } = useQuery<Application[]>({
    queryKey: ['/api/applications/project', id],
    enabled: !!id,
  });

  const applyMutation = useMutation({
    mutationFn: async (data: { proposal: string; quotedPrice: string }) => {
      const response = await apiRequest('POST', '/api/applications', {
        projectId: id,
        proposal: data.proposal,
        quotedPrice: parseFloat(data.quotedPrice),
        estimatedDuration: '2-3 weeks',
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/applications/project', id] });
      toast({
        title: 'Application Submitted',
        description: 'Your application has been sent to the client.',
      });
      setProposal('');
      setQuotedPrice('');
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to submit application. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleApply = () => {
    if (!proposal.trim() || !quotedPrice.trim()) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    applyMutation.mutate({ proposal, quotedPrice });
  };

  if (projectLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <LoadingSpinner />
        </div>
      </DashboardLayout>
    );
  }

  if (!project) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto px-4 py-8">
          <EmptyState
            title="Project Not Found"
            description="The project you're looking for doesn't exist or has been removed."
            icon={<User className="h-6 w-6" />}
          />
        </div>
      </DashboardLayout>
    );
  }

  const canApply = user?.role === 'developer' && project.status === 'open';
  const hasApplied = applications?.some(app => app.developerId === user?.id);

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Project Header */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl" data-testid="text-project-title">
                      {project.title}
                    </CardTitle>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span data-testid="text-project-created">
                          Posted {formatDistanceToNow(new Date(project.createdAt || ''), { addSuffix: true })}
                        </span>
                      </div>
                      <Badge variant={project.status === 'open' ? 'default' : 'secondary'} data-testid="badge-project-status">
                        {project.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center text-accent font-bold text-xl">
                      <DollarSign className="h-5 w-5 mr-1" />
                      <span data-testid="text-project-budget">{project.budget}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <p className="text-muted-foreground" data-testid="text-project-description">
                    {project.description}
                  </p>
                </div>
                
                {project.requiredSkills && project.requiredSkills.length > 0 && (
                  <div className="mt-6">
                    <h3 className="font-semibold text-foreground mb-3">Required Skills</h3>
                    <div className="flex flex-wrap gap-2">
                      {project.requiredSkills.map((skill, index) => (
                        <Badge key={index} variant="secondary" data-testid={`badge-skill-${index}`}>
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Application Form */}
            {canApply && !hasApplied && (
              <Card>
                <CardHeader>
                  <CardTitle>Submit Your Proposal</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Your Proposal
                    </label>
                    <Textarea
                      placeholder="Describe your approach to this project, your relevant experience, and why you're the right fit..."
                      value={proposal}
                      onChange={(e) => setProposal(e.target.value)}
                      rows={6}
                      data-testid="textarea-proposal"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Your Quote ($)
                    </label>
                    <Input
                      type="number"
                      placeholder="Enter your quote for this project"
                      value={quotedPrice}
                      onChange={(e) => setQuotedPrice(e.target.value)}
                      data-testid="input-quote"
                    />
                  </div>
                  <Button 
                    onClick={handleApply}
                    disabled={applyMutation.isPending}
                    className="w-full"
                    data-testid="button-submit-proposal"
                  >
                    {applyMutation.isPending ? (
                      <LoadingSpinner className="h-4 w-4 mr-2" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    Submit Proposal
                  </Button>
                </CardContent>
              </Card>
            )}

            {hasApplied && (
              <Card className="bg-accent/10 border-accent/20">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-accent rounded-full flex items-center justify-center">
                      <Send className="h-6 w-6 text-accent-foreground" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">Application Submitted</h3>
                      <p className="text-sm text-muted-foreground">
                        You have already applied to this project. The client will review your proposal.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Info */}
            <Card>
              <CardHeader>
                <CardTitle>Project Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Budget</span>
                  <span className="font-semibold text-foreground" data-testid="text-sidebar-budget">
                    ${project.budget}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <Badge variant="outline" data-testid="badge-sidebar-status">
                    {project.status}
                  </Badge>
                </div>
                {project.deadline && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Deadline</span>
                    <span className="text-sm font-medium text-foreground" data-testid="text-project-deadline">
                      {new Date(project.deadline).toLocaleDateString()}
                    </span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Applications</span>
                  <span className="font-semibold text-foreground" data-testid="text-applications-count">
                    {applications?.length || 0}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Applications List */}
            <Card>
              <CardHeader>
                <CardTitle>
                  Applications ({applications?.length || 0})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {applicationsLoading ? (
                  <div className="flex justify-center py-4">
                    <LoadingSpinner className="h-6 w-6" />
                  </div>
                ) : applications && applications.length > 0 ? (
                  <div className="space-y-3">
                    {applications.map((application) => (
                      <ApplicationCard key={application.id} application={application} />
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title="No Applications"
                    description="No developers have applied to this project yet."
                    icon={<User className="h-4 w-4" />}
                  />
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
