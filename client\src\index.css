@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(222, 16%, 13%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(222, 16%, 13%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(222, 16%, 13%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(215, 25%, 27%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215, 16%, 47%);
  --accent: hsl(142, 76%, 36%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --ring: hsl(217, 91%, 60%);
  --chart-1: hsl(217, 91%, 60%);
  --chart-2: hsl(142, 76%, 36%);
  --chart-3: hsl(42, 93%, 56%);
  --chart-4: hsl(147, 79%, 42%);
  --chart-5: hsl(341, 75%, 51%);
  --sidebar: hsl(210, 40%, 96%);
  --sidebar-foreground: hsl(222, 16%, 13%);
  --sidebar-primary: hsl(217, 91%, 60%);
  --sidebar-primary-foreground: hsl(0, 0%, 98%);
  --sidebar-accent: hsl(210, 40%, 96%);
  --sidebar-accent-foreground: hsl(217, 91%, 60%);
  --sidebar-border: hsl(214, 32%, 91%);
  --sidebar-ring: hsl(217, 91%, 60%);
  --font-sans: "Inter", system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: "JetBrains Mono", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00), 0px 1px 2px -1px hsl(217, 91%, 60%, 0.00);
  --shadow: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00), 0px 1px 2px -1px hsl(217, 91%, 60%, 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00), 0px 2px 4px -1px hsl(217, 91%, 60%, 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00), 0px 4px 6px -1px hsl(217, 91%, 60%, 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00), 0px 8px 10px -1px hsl(217, 91%, 60%, 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(217, 91%, 60%, 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 85%);
  --card: hsl(228, 10%, 10%);
  --card-foreground: hsl(0, 0%, 85%);
  --popover: hsl(0, 0%, 0%);
  --popover-foreground: hsl(0, 0%, 85%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(215, 25%, 27%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --muted: hsl(228, 10%, 15%);
  --muted-foreground: hsl(215, 16%, 47%);
  --accent: hsl(142, 76%, 36%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --border: hsl(215, 25%, 27%);
  --input: hsl(215, 25%, 27%);
  --ring: hsl(217, 91%, 60%);
  --chart-1: hsl(217, 91%, 60%);
  --chart-2: hsl(142, 76%, 36%);
  --chart-3: hsl(42, 93%, 56%);
  --chart-4: hsl(147, 79%, 42%);
  --chart-5: hsl(341, 75%, 51%);
  --sidebar: hsl(228, 10%, 10%);
  --sidebar-foreground: hsl(0, 0%, 85%);
  --sidebar-primary: hsl(217, 91%, 60%);
  --sidebar-primary-foreground: hsl(0, 0%, 98%);
  --sidebar-accent: hsl(228, 10%, 15%);
  --sidebar-accent-foreground: hsl(217, 91%, 60%);
  --sidebar-border: hsl(215, 25%, 27%);
  --sidebar-ring: hsl(217, 91%, 60%);
  --font-sans: "Inter", system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: "JetBrains Mono", monospace;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
    font-family: var(--font-sans);
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  .mono {
    font-family: var(--font-mono);
  }
}

@layer utilities {
  .active-nav {
    background-color: var(--primary);
    color: var(--primary-foreground);
  }

  .notification-dot {
    animation: pulse 2s infinite;
  }

  .project-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
  }

  .skill-tag {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
