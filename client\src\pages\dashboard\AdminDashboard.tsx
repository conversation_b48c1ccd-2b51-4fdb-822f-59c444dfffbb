import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { Shield, Users, Briefcase, AlertTriangle, TrendingUp } from 'lucide-react';
import { User, Project } from '@shared/schema';

export default function AdminDashboard() {
  const { data: users, isLoading: usersLoading } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ['/api/projects'],
  });

  const stats = [
    {
      title: 'Total Users',
      value: users?.length || 0,
      change: '+12 this week',
      icon: Users,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: 'Active Projects',
      value: projects?.filter(p => p.status === 'in_progress').length || 0,
      change: '+5 this week',
      icon: Briefcase,
      color: 'text-accent',
      bgColor: 'bg-accent/10',
    },
    {
      title: 'Total Revenue',
      value: '$125,400',
      change: '+8.2% this month',
      icon: TrendingUp,
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900',
    },
    {
      title: 'Pending Reports',
      value: '3',
      change: 'Needs attention',
      icon: AlertTriangle,
      color: 'text-destructive',
      bgColor: 'bg-destructive/10',
    },
  ];

  const developers = users?.filter(u => u.role === 'developer') || [];
  const clients = users?.filter(u => u.role === 'client') || [];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Dashboard Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Admin Dashboard</h1>
            <p className="text-muted-foreground mt-1">Platform management and user oversight</p>
          </div>
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Badge variant="secondary" className="bg-primary text-primary-foreground" data-testid="badge-admin-role">
              <Shield className="h-3 w-3 mr-1" />
              Administrator
            </Badge>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">{stat.title}</p>
                    <p className="text-2xl font-bold text-foreground" data-testid={`stat-value-${index}`}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="flex items-center mt-3 text-sm">
                  <span className="text-accent" data-testid={`stat-change-${index}`}>{stat.change}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* User Management */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-foreground">User Management</h2>
                <Button variant="ghost" size="sm" data-testid="button-manage-users">
                  Manage All
                </Button>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-background rounded-lg border">
                  <div>
                    <p className="font-medium text-foreground">Developers</p>
                    <p className="text-sm text-muted-foreground" data-testid="text-developers-count">
                      {developers.length} registered
                    </p>
                  </div>
                  <Badge variant="secondary" data-testid="badge-developers-online">
                    {developers.filter(d => d.isAvailable).length} online
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-background rounded-lg border">
                  <div>
                    <p className="font-medium text-foreground">Clients</p>
                    <p className="text-sm text-muted-foreground" data-testid="text-clients-count">
                      {clients.length} registered
                    </p>
                  </div>
                  <Badge variant="secondary" data-testid="badge-active-clients">
                    {clients.length} active
                  </Badge>
                </div>
              </div>

              {usersLoading ? (
                <div className="flex justify-center py-4">
                  <LoadingSpinner className="h-6 w-6" />
                </div>
              ) : users && users.length > 0 ? (
                <div className="mt-6">
                  <h3 className="font-medium text-foreground mb-3">Recent Registrations</h3>
                  <div className="space-y-2">
                    {users.slice(0, 3).map((user) => (
                      <div key={user.id} className="flex items-center justify-between text-sm">
                        <span className="text-foreground" data-testid={`text-user-name-${user.id}`}>
                          {user.fullName}
                        </span>
                        <Badge variant="outline" data-testid={`badge-user-role-${user.id}`}>
                          {user.role}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <EmptyState
                  title="No Users"
                  description="No users registered yet"
                  icon={<Users className="h-6 w-6" />}
                />
              )}
            </CardContent>
          </Card>

          {/* Project Management */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-foreground">Project Management</h2>
                <Button variant="ghost" size="sm" data-testid="button-manage-projects">
                  Manage All
                </Button>
              </div>
              
              {projectsLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : projects && projects.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-background rounded-lg border">
                      <p className="text-2xl font-bold text-foreground" data-testid="text-open-projects">
                        {projects.filter(p => p.status === 'open').length}
                      </p>
                      <p className="text-xs text-muted-foreground">Open</p>
                    </div>
                    <div className="text-center p-3 bg-background rounded-lg border">
                      <p className="text-2xl font-bold text-foreground" data-testid="text-completed-projects">
                        {projects.filter(p => p.status === 'completed').length}
                      </p>
                      <p className="text-xs text-muted-foreground">Completed</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-foreground mb-3">Recent Projects</h3>
                    <div className="space-y-2">
                      {projects.slice(0, 3).map((project) => (
                        <div key={project.id} className="flex items-center justify-between text-sm">
                          <span className="text-foreground truncate flex-1" data-testid={`text-project-title-${project.id}`}>
                            {project.title}
                          </span>
                          <Badge 
                            variant={project.status === 'completed' ? 'default' : 'secondary'}
                            data-testid={`badge-project-status-${project.id}`}
                          >
                            {project.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <EmptyState
                  title="No Projects"
                  description="No projects created yet"
                  icon={<Briefcase className="h-6 w-6" />}
                />
              )}
            </CardContent>
          </Card>

          {/* Platform Health */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-6">Platform Health</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">System Status</span>
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" data-testid="badge-system-status">
                    Operational
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Database</span>
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" data-testid="badge-database-status">
                    Healthy
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">API Response Time</span>
                  <span className="text-sm font-medium text-foreground" data-testid="text-api-response">
                    150ms avg
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Active Sessions</span>
                  <span className="text-sm font-medium text-foreground" data-testid="text-active-sessions">
                    24
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reports & Moderation */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-6">Reports & Moderation</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-destructive/10 rounded-lg border border-destructive/20">
                  <div>
                    <p className="font-medium text-foreground">Pending Reports</p>
                    <p className="text-sm text-muted-foreground">Requires immediate attention</p>
                  </div>
                  <Badge variant="destructive" data-testid="badge-pending-reports">
                    3
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-background rounded-lg border">
                  <div>
                    <p className="font-medium text-foreground">Resolved Reports</p>
                    <p className="text-sm text-muted-foreground">This month</p>
                  </div>
                  <span className="font-semibold text-foreground" data-testid="text-resolved-reports">
                    12
                  </span>
                </div>
                
                <Button className="w-full" data-testid="button-view-reports">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View All Reports
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
