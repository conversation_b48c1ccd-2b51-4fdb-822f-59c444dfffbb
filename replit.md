# Overview

DevLinkr is a full-stack freelance developer marketplace application that connects clients with skilled developers. The platform allows clients to post projects and developers to browse opportunities, submit applications, and communicate through an integrated messaging system. Built as a modern web application with a React frontend and Express.js backend, it leverages Firebase for authentication and PostgreSQL for data persistence.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
The client-side application is built with React and TypeScript, using Vite as the build tool. The UI is constructed with shadcn/ui components built on top of Radix UI primitives, providing a comprehensive design system with dark mode support. The application uses <PERSON><PERSON><PERSON> for client-side routing and TanStack Query for server state management. The architecture follows a component-based approach with reusable UI components, custom hooks for business logic, and context providers for global state management (authentication and theming).

## Backend Architecture
The server is built with Express.js and TypeScript, following a RESTful API design. The application uses a layered architecture with separate modules for routes, storage/database operations, and business logic. Firebase Admin SDK handles authentication verification on the server side, while the storage layer abstracts database operations through a clean interface pattern. WebSocket support is integrated for real-time messaging features.

## Database Design
The application uses PostgreSQL with Drizzle ORM for type-safe database operations. The schema defines core entities including users (with role-based access for clients, developers, and admins), projects, applications, reviews, and messages. The database design supports the freelance marketplace workflow with proper relationships between entities and enum types for status management.

## Authentication & Authorization
Firebase Authentication provides the foundation for user management, supporting Google OAuth integration. The backend verifies Firebase tokens on protected routes, while the frontend maintains authentication state through React Context. User roles (client, developer, admin) are managed at the database level with role-based access control implemented in the API layer.

## State Management
The frontend uses multiple state management strategies: React Context for global state (auth, theme), TanStack Query for server state and caching, and local component state for UI interactions. This hybrid approach optimizes performance while maintaining clean separation of concerns.

## Real-time Features
WebSocket integration enables real-time messaging between clients and developers. The system includes connection management, automatic reconnection logic, and message delivery confirmation to ensure reliable communication.

# External Dependencies

## Database Services
- **Neon Database**: Serverless PostgreSQL database hosting with connection pooling
- **Drizzle ORM**: Type-safe database toolkit for schema management and queries

## Authentication & User Management
- **Firebase Authentication**: User authentication service with OAuth providers
- **Firebase Admin SDK**: Server-side user verification and management

## Frontend Libraries
- **Radix UI**: Headless UI component primitives for accessibility and functionality
- **shadcn/ui**: Pre-built component library with consistent design patterns
- **TanStack Query**: Server state management and caching solution
- **Wouter**: Lightweight client-side routing library

## Development & Build Tools
- **Vite**: Fast build tool and development server with HMR support
- **TypeScript**: Static type checking across the entire application
- **Tailwind CSS**: Utility-first CSS framework for styling
- **ESBuild**: Fast JavaScript bundler for production builds

## Real-time Communication
- **WebSocket (ws)**: Native WebSocket implementation for real-time messaging
- **connect-pg-simple**: Session store for PostgreSQL integration

## Utilities & Helpers
- **Zod**: Runtime type validation and schema parsing
- **date-fns**: Modern date utility library for formatting and manipulation
- **clsx & class-variance-authority**: Conditional CSS class management