import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, DollarSign } from 'lucide-react';
import { Project } from '@shared/schema';
import { formatDistanceToNow } from 'date-fns';

interface ProjectCardProps {
  project: Project & {
    client?: { fullName: string; avatar?: string };
    _count?: { applications: number };
  };
  onApply?: (projectId: string) => void;
  showApplyButton?: boolean;
}

export function ProjectCard({ project, onApply, showApplyButton = true }: ProjectCardProps) {
  const handleApply = () => {
    if (onApply) {
      onApply(project.id);
    }
  };

  return (
    <Card className="project-card hover:shadow-md transition-all hover:translate-y-[-2px]" data-testid={`card-project-${project.id}`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-3">
          <h3 className="font-semibold text-foreground text-lg" data-testid={`text-title-${project.id}`}>
            {project.title}
          </h3>
          <div className="flex items-center text-accent font-semibold" data-testid={`text-budget-${project.id}`}>
            <DollarSign className="h-4 w-4 mr-1" />
            {project.budget}
          </div>
        </div>
        
        <p className="text-muted-foreground text-sm mb-4 line-clamp-3" data-testid={`text-description-${project.id}`}>
          {project.description}
        </p>
        
        {project.requiredSkills && project.requiredSkills.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {project.requiredSkills.map((skill, index) => (
              <Badge key={index} variant="secondary" className="text-xs" data-testid={`badge-skill-${project.id}-${index}`}>
                {skill}
              </Badge>
            ))}
          </div>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-muted-foreground space-x-4">
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span data-testid={`text-created-${project.id}`}>
                {formatDistanceToNow(new Date(project.createdAt || ''), { addSuffix: true })}
              </span>
            </div>
            {project._count && (
              <span data-testid={`text-applications-${project.id}`}>
                {project._count.applications} applications
              </span>
            )}
          </div>
          
          {showApplyButton && (
            <Button onClick={handleApply} data-testid={`button-apply-${project.id}`}>
              Apply Now
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
