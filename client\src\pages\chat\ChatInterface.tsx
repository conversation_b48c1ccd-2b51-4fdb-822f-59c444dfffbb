import React from 'react';
import { useParams } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { Send, MessageCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Message, User } from '@shared/schema';
import { formatDistanceToNow } from 'date-fns';

export default function ChatInterface() {
  const { userId: targetUserId } = useParams();
  const { user } = useAuth();
  const [message, setMessage] = React.useState('');
  const [selectedUserId, setSelectedUserId] = React.useState<string | null>(targetUserId || null);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  const { data: conversations, isLoading: conversationsLoading } = useQuery<Message[]>({
    queryKey: ['/api/conversations'],
    enabled: !!user?.id,
  });

  const { data: messages, isLoading: messagesLoading } = useQuery<Message[]>({
    queryKey: ['/api/messages', selectedUserId],
    enabled: !!user?.id && !!selectedUserId,
  });

  const { data: users } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  const { sendMessage } = useWebSocket({
    onMessage: (data) => {
      // Handle incoming messages
      if (data.type === 'new_message') {
        // Refresh messages if it's for the current conversation
        if (data.data.senderId === selectedUserId || data.data.receiverId === selectedUserId) {
          // Could implement optimistic updates here
        }
      }
    },
  });

  React.useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!message.trim() || !selectedUserId || !user) return;

    try {
      await sendMessage({
        type: 'send_message',
        senderId: user.id,
        receiverId: selectedUserId,
        content: message.trim(),
      });
      setMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Get unique conversation partners
  const conversationPartners = React.useMemo(() => {
    if (!conversations || !user) return [];

    const partnerMap = new Map<string, { user: User; lastMessage: Message }>();

    conversations.forEach((msg) => {
      const partnerId = msg.senderId === user.id ? msg.receiverId : msg.senderId;
      const partner = users?.find(u => u.id === partnerId);
      
      if (partner && (!partnerMap.has(partnerId) || (msg.createdAt && partnerMap.get(partnerId)!.lastMessage.createdAt && new Date(msg.createdAt) > new Date(partnerMap.get(partnerId)!.lastMessage.createdAt)))) {
        partnerMap.set(partnerId, { user: partner, lastMessage: msg });
      }
    });

    return Array.from(partnerMap.values()).sort((a, b) => 
      new Date(b.lastMessage.createdAt || '').getTime() - new Date(a.lastMessage.createdAt || '').getTime()
    );
  }, [conversations, users, user]);

  const selectedUser = users?.find(u => u.id === selectedUserId);

  if (!user) return null;

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground">Messages</h1>
          <p className="text-muted-foreground mt-1">
            Communicate with clients and developers
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
          {/* Conversations List */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Conversations</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {conversationsLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : conversationPartners.length > 0 ? (
                <div className="space-y-1">
                  {conversationPartners.map(({ user: partner, lastMessage }) => (
                    <div
                      key={partner.id}
                      className={`flex items-start space-x-3 p-4 hover:bg-muted cursor-pointer transition-colors ${
                        selectedUserId === partner.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => setSelectedUserId(partner.id)}
                      data-testid={`conversation-${partner.id}`}
                    >
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={partner.avatar || undefined} />
                        <AvatarFallback>{partner.fullName?.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-foreground truncate" data-testid={`text-partner-name-${partner.id}`}>
                            {partner.fullName}
                          </p>
                          <span className="text-xs text-muted-foreground" data-testid={`text-message-time-${partner.id}`}>
                            {formatDistanceToNow(new Date(lastMessage.createdAt || ''), { addSuffix: true })}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground truncate" data-testid={`text-message-preview-${partner.id}`}>
                          {lastMessage.content}
                        </p>
                        <Badge variant="outline" className="mt-1 text-xs">
                          {partner.role}
                        </Badge>
                        {!lastMessage.isRead && lastMessage.senderId !== user.id && (
                          <div className="w-2 h-2 bg-primary rounded-full mt-1" data-testid={`indicator-unread-${partner.id}`} />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6">
                  <EmptyState
                    title="No Conversations"
                    description="Start a conversation by applying to a project or contacting a developer"
                    icon={<MessageCircle className="h-6 w-6" />}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Chat Area */}
          <Card className="lg:col-span-2 flex flex-col">
            {selectedUser ? (
              <>
                <CardHeader className="border-b">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={selectedUser.avatar || undefined} />
                      <AvatarFallback>{selectedUser.fullName?.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg" data-testid="text-chat-partner-name">
                        {selectedUser.fullName}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground" data-testid="text-chat-partner-role">
                        {selectedUser.role}
                      </p>
                    </div>
                    {selectedUser.isAvailable && (
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Online
                      </Badge>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messagesLoading ? (
                    <div className="flex justify-center py-8">
                      <LoadingSpinner />
                    </div>
                  ) : messages && messages.length > 0 ? (
                    <>
                      {messages.map((msg) => {
                        const isOwn = msg.senderId === user.id;
                        return (
                          <div
                            key={msg.id}
                            className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
                            data-testid={`message-${msg.id}`}
                          >
                            <div
                              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                isOwn
                                  ? 'bg-primary text-primary-foreground'
                                  : 'bg-muted text-muted-foreground'
                              }`}
                            >
                              <p className="text-sm" data-testid={`text-message-content-${msg.id}`}>
                                {msg.content}
                              </p>
                              <p className={`text-xs mt-1 ${isOwn ? 'text-primary-foreground/70' : 'text-muted-foreground'}`} data-testid={`text-message-timestamp-${msg.id}`}>
                                {formatDistanceToNow(new Date(msg.createdAt || ''), { addSuffix: true })}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </>
                  ) : (
                    <EmptyState
                      title="No Messages"
                      description="Start the conversation by sending a message"
                      icon={<MessageCircle className="h-6 w-6" />}
                    />
                  )}
                </CardContent>

                <div className="border-t p-4">
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Type your message..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      className="flex-1"
                      data-testid="input-message"
                    />
                    <Button onClick={handleSendMessage} disabled={!message.trim()} data-testid="button-send-message">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <CardContent className="flex-1 flex items-center justify-center">
                <EmptyState
                  title="Select a Conversation"
                  description="Choose a conversation from the left to start messaging"
                  icon={<MessageCircle className="h-8 w-8" />}
                />
              </CardContent>
            )}
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
