import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { ProjectCard } from '@/components/ProjectCard';
import { Briefcase, Users, DollarSign, Clock, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Project, User } from '@shared/schema';
import { Link } from 'wouter';

export default function ClientDashboard() {
  const { user } = useAuth();

  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ['/api/projects'],
    enabled: !!user,
  });

  const { data: developers, isLoading: developersLoading } = useQuery<User[]>({
    queryKey: ['/api/users'],
    enabled: !!user,
  });

  if (!user) return null;

  const myProjects = projects?.filter(p => p.clientId === user.id) || [];

  const stats = [
    {
      title: 'Active Projects',
      value: myProjects.filter(p => p.status === 'in_progress').length,
      change: '2 in progress',
      icon: Briefcase,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: 'Total Projects',
      value: myProjects.length,
      change: '+1 this month',
      icon: Briefcase,
      color: 'text-accent',
      bgColor: 'bg-accent/10',
    },
    {
      title: 'Available Developers',
      value: developers?.filter(d => d.isAvailable).length || 0,
      change: 'Ready to hire',
      icon: Users,
      color: 'text-accent',
      bgColor: 'bg-accent/10',
    },
    {
      title: 'Total Spent',
      value: '$18,500',
      change: '+$2,100 this month',
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900',
    },
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Dashboard Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Client Dashboard</h1>
            <p className="text-muted-foreground mt-1">Manage your projects and find talented developers</p>
          </div>
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Button asChild data-testid="button-post-project">
              <Link href="/projects/create">
                <Plus className="h-4 w-4 mr-2" />
                Post New Project
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">{stat.title}</p>
                    <p className="text-2xl font-bold text-foreground" data-testid={`stat-value-${index}`}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="flex items-center mt-3 text-sm">
                  <span className="text-accent" data-testid={`stat-change-${index}`}>{stat.change}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* My Projects */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-foreground">My Projects</h2>
                  <Button variant="ghost" size="sm" asChild data-testid="link-view-all-projects">
                    <Link href="/projects">View All</Link>
                  </Button>
                </div>
                {projectsLoading ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner />
                  </div>
                ) : myProjects.length > 0 ? (
                  <div className="space-y-4">
                    {myProjects.slice(0, 3).map((project) => (
                      <ProjectCard 
                        key={project.id} 
                        project={project}
                        showApplyButton={false}
                      />
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title="No Projects Yet"
                    description="Start by posting your first project to find talented developers"
                    icon={<Briefcase className="h-6 w-6" />}
                    action={
                      <Button asChild data-testid="button-create-first-project">
                        <Link href="/projects/create">Post Your First Project</Link>
                      </Button>
                    }
                  />
                )}
              </CardContent>
            </Card>

            {/* Featured Developers */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-foreground">Featured Developers</h2>
                  <Button variant="ghost" size="sm" asChild data-testid="link-browse-developers">
                    <Link href="/developers">Browse All</Link>
                  </Button>
                </div>
                {developersLoading ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner />
                  </div>
                ) : developers && developers.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {developers.slice(0, 4).map((developer) => (
                      <Card key={developer.id} className="hover:shadow-md transition-all">
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                              <span className="text-primary-foreground font-semibold">
                                {developer.fullName.charAt(0)}
                              </span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-foreground truncate" data-testid={`text-dev-name-${developer.id}`}>
                                {developer.fullName}
                              </h3>
                              <p className="text-sm text-muted-foreground truncate" data-testid={`text-dev-role-${developer.id}`}>
                                {developer.role}
                              </p>
                              <div className="flex items-center mt-1">
                                {developer.hourlyRate && (
                                  <span className="text-xs bg-accent text-accent-foreground px-2 py-1 rounded-full" data-testid={`text-dev-rate-${developer.id}`}>
                                    ${developer.hourlyRate}/hr
                                  </span>
                                )}
                                {developer.isAvailable && (
                                  <Badge variant="secondary" className="ml-2 text-xs" data-testid={`badge-dev-available-${developer.id}`}>
                                    Available
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          {developer.skills && (
                            <div className="flex flex-wrap gap-1 mt-3">
                              {developer.skills.slice(0, 3).map((skill, index) => (
                                <Badge key={index} variant="outline" className="text-xs" data-testid={`badge-dev-skill-${developer.id}-${index}`}>
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title="No Developers Available"
                    description="Check back later for available developers"
                    icon={<Users className="h-6 w-6" />}
                  />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-foreground mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Button asChild className="w-full justify-start" data-testid="button-quick-post-project">
                    <Link href="/projects/create">
                      <Plus className="h-4 w-4 mr-2" />
                      Post New Project
                    </Link>
                  </Button>
                  <Button variant="outline" asChild className="w-full justify-start" data-testid="button-quick-browse-developers">
                    <Link href="/developers">
                      <Users className="h-4 w-4 mr-2" />
                      Browse Developers
                    </Link>
                  </Button>
                  <Button variant="outline" asChild className="w-full justify-start" data-testid="button-quick-view-messages">
                    <Link href="/chat">
                      <Clock className="h-4 w-4 mr-2" />
                      View Messages
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-foreground mb-4">Recent Activity</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-accent rounded-full" />
                    <span className="text-muted-foreground" data-testid="text-activity-1">
                      New application received for "E-commerce App"
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full" />
                    <span className="text-muted-foreground" data-testid="text-activity-2">
                      Project "Dashboard Redesign" completed
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                    <span className="text-muted-foreground" data-testid="text-activity-3">
                      New message from Alex Chen
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Project Statistics */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-foreground mb-4">Project Statistics</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Success Rate</span>
                    <span className="font-semibold text-foreground" data-testid="text-success-rate">92%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Avg. Completion Time</span>
                    <span className="font-semibold text-foreground" data-testid="text-avg-completion">2.3 weeks</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Total Applications</span>
                    <span className="font-semibold text-foreground" data-testid="text-total-applications">47</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
