import React, { useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Code } from 'lucide-react';
import { signInWithGoogle, handleRedirect } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useLocation } from 'wouter';

export default function Login() {
  const { firebaseUser, loading } = useAuth();
  const [, setLocation] = useLocation();

  useEffect(() => {
    if (firebaseUser) {
      setLocation('/dashboard');
    }
  }, [firebaseUser, setLocation]);

  useEffect(() => {
    handleRedirect().catch(console.error);
  }, []);

  const handleSignIn = async () => {
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Sign-in error:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <Code className="h-6 w-6 text-primary-foreground" />
            </div>
            <span className="text-2xl font-bold text-foreground">DevLinkr</span>
          </div>
          <CardTitle className="text-xl">Welcome to DevLinkr</CardTitle>
          <p className="text-muted-foreground">
            Connect developers and clients for commissioned projects
          </p>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={handleSignIn} 
            className="w-full"
            data-testid="button-sign-in"
          >
            Sign in with Google
          </Button>
          <p className="text-xs text-muted-foreground text-center mt-4">
            By signing in, you agree to our Terms of Service and Privacy Policy
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
